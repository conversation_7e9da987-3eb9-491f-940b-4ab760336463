import { Body, Controller, Get, Post } from '@nestjs/common';
import { CallService } from './call.service';
import { ApiTags } from '@nestjs/swagger';
import { CallInfoDto } from './dtos/call-info.dto';

@ApiTags('voice')
@Controller('voice')
export class CallController {
  constructor(private readonly callService: CallService) {}

  @Get('incoming')
  startGet(@Body() createCallDto: CallInfoDto) {
    console.log('get', createCallDto);
  }

  @Post('incoming')
  start(@Body() callInfoDto: CallInfoDto) {
    console.log('data', callInfoDto);

    if (callInfoDto.dynamic?.lastapp === 'firststart') {
      return { application: 'STREAM', volume: 8, timeout: 30, wait: 1 };
    }
    if (callInfoDto.dynamic?.lastapp === 'ENDSTREAM') {
      return { status: 'end' };
    }

    // return  createCallDto;
    // return {
    //   application: 'TTS',
    //   action: 'SAYIN KULLANICI ARACINIZ HAZIR',
    //   responseurl: '',
    //   gender: 'male',
    //   language: 'tr',
    //   rate: '',
    //   pitch: '',
    //   variable: '',
    //   variable2: '',
    //   dtmfwait: '4',
    //   dtmflen: '1',
    //   responsewait: '5',
    //   beforeplayback: '',
    // };
    return this.callService.incoming(callInfoDto);
  }
}
