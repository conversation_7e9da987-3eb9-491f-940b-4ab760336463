import { Injectable } from '@nestjs/common';
import * as WebSocket from 'ws';
@Injectable()
export class SendVoiceService {
  // wss://echo.websocket.org is a test websocket server
  private ws: WebSocket;

  constructor() {}

  connect(wsUrl) {
    this.ws = new WebSocket(wsUrl);
    this.ws.on('open', () => {
      console.log('send connected');
      // this.ws.send(Math.random());
    });

    this.ws.on('message', function (message) {
      console.log('message');
      console.log(message);
    });
  }

  sendVoice(data: { streamfile: string; uniqueid: string }) {
    this.ws.send(data);
  }

  onMessage(handler: Function) {
    this.ws.on('message', handler);
  }
}
