import { Injectable } from '@nestjs/common';
import { CallInfoDto } from './dtos/call-info.dto';
import { WSService } from './wss.service';
import { delay, of } from 'rxjs';
import { SendVoiceService } from './send-voice.service';

@Injectable()
export class CallService {
  public constructor(
    protected readonly wsService: WSService,
    protected readonly sendVoiceService: SendVoiceService,
    ) {}

  public incoming(callInfo: CallInfoDto) {
    this.wsService.connect(callInfo.dynamic.tx_socket);
    this.sendVoiceService.connect(callInfo.dynamic.rx_socket);


    this.wsService.onMessage((message) => {
      console.log('onmesage', message);
      
      // Create a unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `./uploads/call-recording-${callInfo.dynamic.uniqueid}-${timestamp}.wav`;


      const parsed = JSON.parse(message);
      console.log('parsed', parsed);
      // Save the binary data as a WAV file
      const fs = require('fs');
      fs.writeFileSync(filename, parsed.stream);


      this.sendVoiceService.sendVoice({
        streamfile: parsed.stream,
        uniqueid: callInfo.dynamic.uniqueid,
      });
      
      console.log(`Saved audio file: ${filename}`);
    });


    return  of({}).pipe(delay(5000)).toPromise().then(() => {
      return { status: 'processing' };
    });
  }
}
